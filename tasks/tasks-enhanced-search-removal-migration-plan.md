# Tasks: Enhanced Search Removal & Migration Plan

## Relevant Files

- `src/lib/search/enhanced-normal-search.ts` - New unified search backend combining Fuse.js with filtering capabilities.
- `src/lib/search/enhanced-normal-search.test.ts` - Unit tests for the enhanced normal search functionality.
- `src/ui/search/search-filters.tsx` - Filter components for category, price range, and sorting on normal search page.
- `src/ui/search/search-filters.test.tsx` - Unit tests for search filter components.
- `src/ui/products/enhanced-product-list.tsx` - Enhanced product list component with optional add-to-cart functionality.
- `src/ui/products/enhanced-product-list.test.tsx` - Unit tests for enhanced product list component.
- `src/app/(store)/search/page.tsx` - Normal search results page with integrated filters (MODIFY).
- `src/app/(store)/search/page.test.tsx` - Unit tests for search page component.
- `src/lib/search/search.ts` - Update to use new enhanced backend (MODIFY).
- `src/lib/search/search.test.ts` - Unit tests for updated search functionality.
- `src/ui/nav/search-input.client.tsx` - Add search suggestions support (MODIFY).
- `src/ui/nav/search-input.client.test.tsx` - Unit tests for enhanced search input.
- `src/app/(store)/layout.tsx` - Remove EnhancedSearch component import and usage (MODIFY).
- `package.json` - Remove unused dependencies (MODIFY).
- `src/lib/search/enhanced-search.ts` - Enhanced search backend (DELETE).
- `src/ui/enhanced-search.tsx` - Enhanced search modal component (DELETE).
- `src/ui/enhanced-search/product-list.tsx` - Enhanced search product list (DELETE).
- `src/app/api/search/route.ts` - Enhanced search API endpoints (DELETE).
- `src/app/api/recommendations/route.ts` - Product recommendations API (DELETE).
- `src/ui/product-recommendations.tsx` - Product recommendations component (DELETE).
- `CLAUDE.md` - Update documentation to reflect changes (MODIFY).

### Notes

- Unit tests should be placed alongside the code files they are testing.
- Use `npx jest [optional/path/to/test/file]` to run tests. Running without a path executes all tests found by the Jest configuration.
- Before deleting files, ensure all references are removed to prevent build errors.
- Test the migration thoroughly in a development environment before deploying.
- Consider implementing feature flags for gradual rollout if needed.

## Tasks

- [ ] 1.0 Create Enhanced Normal Search Backend
  - [ ] 1.1 Create `src/lib/search/enhanced-normal-search.ts` with TypeScript interfaces for `NormalSearchFilters` and `NormalSearchResult`
  - [ ] 1.2 Implement Fuse.js search index creation and caching logic (copy from enhanced-search.ts)
  - [ ] 1.3 Create `enhancedNormalSearch` function with fuzzy search, filtering, and sorting capabilities
  - [ ] 1.4 Implement category filtering logic to filter products by metadata.category
  - [ ] 1.5 Implement price range filtering logic using product.default_price.unit_amount
  - [ ] 1.6 Implement sorting functionality (relevance, price-asc, price-desc, name, newest)
  - [ ] 1.7 Add metadata extraction function to get available categories and price ranges
  - [ ] 1.8 Add proper error handling and fallback for search failures
  - [ ] 1.9 Implement Next.js unstable_cache for performance optimization
  - [ ] 1.10 Write comprehensive unit tests for all search functions and edge cases

- [ ] 2.0 Build Filter UI Components for Normal Search
  - [ ] 2.1 Create `src/ui/search/search-filters.tsx` with main SearchFilters component
  - [ ] 2.2 Implement category dropdown filter using Select component from shadcn/ui
  - [ ] 2.3 Implement price range slider/input filters with min/max validation
  - [ ] 2.4 Implement sort dropdown with all sorting options (relevance, price-asc, price-desc, name, newest)
  - [ ] 2.5 Add "Clear Filters" button functionality to reset all filters
  - [ ] 2.6 Implement responsive design for mobile and desktop layouts
  - [ ] 2.7 Add proper TypeScript types and props validation
  - [ ] 2.8 Add accessibility features (ARIA labels, keyboard navigation)
  - [ ] 2.9 Implement filter state management and callback handling
  - [ ] 2.10 Write unit tests for all filter components and interactions

- [ ] 3.0 Integrate Filters into Normal Search Results Page
  - [ ] 3.1 Update `src/app/(store)/search/page.tsx` to accept additional URL parameters (category, sort, min_price, max_price)
  - [ ] 3.2 Modify search page to use new `enhancedNormalSearch` instead of simple search
  - [ ] 3.3 Add SearchFilters component to the top of search results page
  - [ ] 3.4 Implement URL parameter synchronization for all filter states
  - [ ] 3.5 Add loading states for search and filter operations
  - [ ] 3.6 Implement proper error handling and fallback UI
  - [ ] 3.7 Maintain existing SEO metadata and page structure
  - [ ] 3.8 Add "No results found" state with filter suggestions
  - [ ] 3.9 Implement debounced search to prevent excessive API calls
  - [ ] 3.10 Write integration tests for search page with filters

- [ ] 4.0 Enhance Product List with Add-to-Cart Functionality
  - [ ] 4.1 Create `src/ui/products/enhanced-product-list.tsx` based on existing ProductList component
  - [ ] 4.2 Add optional "Add to Cart" button to each product card
  - [ ] 4.3 Implement add-to-cart functionality using existing `addToCartAction`
  - [ ] 4.4 Add loading states and success feedback for add-to-cart operations
  - [ ] 4.5 Maintain existing product card design and SEO structure
  - [ ] 4.6 Add proper error handling for failed add-to-cart operations
  - [ ] 4.7 Implement responsive design for add-to-cart buttons
  - [ ] 4.8 Add accessibility features for cart interactions
  - [ ] 4.9 Preserve existing JSON-LD structured data
  - [ ] 4.10 Write unit tests for enhanced product list component

- [ ] 5.0 Remove Enhanced Search System and Clean Up Dependencies
  - [ ] 5.1 Update `src/lib/search/search.ts` to use new `enhancedNormalSearch` function
  - [ ] 5.2 Remove EnhancedSearch component import and usage from `src/app/(store)/layout.tsx`
  - [ ] 5.3 Delete `src/lib/search/enhanced-search.ts` file
  - [ ] 5.4 Delete `src/ui/enhanced-search.tsx` file
  - [ ] 5.5 Delete `src/ui/enhanced-search/product-list.tsx` file
  - [ ] 5.6 Delete `src/app/api/search/route.ts` file
  - [ ] 5.7 Delete `src/app/api/recommendations/route.ts` file
  - [ ] 5.8 Delete `src/ui/product-recommendations.tsx` file
  - [ ] 5.9 Remove unused dependencies from `package.json` (react-select, react-window, react-window-infinite-loader if not used elsewhere)
  - [ ] 5.10 Update `CLAUDE.md` documentation to reflect the changes
  - [ ] 5.11 Run full test suite to ensure no broken imports or references
  - [ ] 5.12 Verify application builds successfully without enhanced search components
  - [ ] 5.13 Test complete search workflow in development environment
  - [ ] 5.14 Update any remaining references to enhanced search in comments or documentation
