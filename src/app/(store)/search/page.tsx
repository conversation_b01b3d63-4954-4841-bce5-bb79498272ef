import { RedirectType, redirect } from "next/navigation";
import type { Metadata } from "next/types";
import { Suspense } from "react";
import { publicUrl } from "@/env.mjs";
import { getTranslations } from "@/i18n/server";
import { SearchPageClient } from "./search-page-client";

export const generateMetadata = async (props: {
	searchParams: Promise<{
		q?: string;
		category?: string;
		sort?: string;
		min_price?: string;
		max_price?: string;
	}>;
}): Promise<Metadata> => {
	const searchParams = await props.searchParams;
	const t = await getTranslations("/search.metadata");
	return {
		title: t("title", { query: searchParams.q }),
		alternates: { canonical: `${publicUrl}/search` },
	};
};

export default async function SearchPage(props: {
	searchParams: Promise<{
		q?: string;
		category?: string;
		sort?: string;
		min_price?: string;
		max_price?: string;
	}>;
}) {
	const searchParams = await props.searchParams;
	const query = searchParams.q;

	if (!query) {
		return redirect("/", RedirectType.replace);
	}

	return (
		<main>
			<Suspense fallback={<div>Loading...</div>}>
				<SearchPageClient initialSearchParams={searchParams} />
			</Suspense>
		</main>
	);
}
