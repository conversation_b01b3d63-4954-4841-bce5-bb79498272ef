"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useDebouncedCallback } from "use-debounce";
import type { Commerce } from "commerce-kit";
import { enhancedNormalSearch, type NormalSearchFilters, type NormalSearchResult } from "@/lib/search/enhanced-normal-search";
import { SearchFilters } from "@/ui/search/search-filters";
import { EnhancedProductList } from "@/ui/products/enhanced-product-list";
import { ProductNotFound } from "@/ui/products/product-not-found";

interface SearchPageClientProps {
	initialSearchParams: {
		q?: string;
		category?: string;
		sort?: string;
		min_price?: string;
		max_price?: string;
	};
}

export function SearchPageClient({ initialSearchParams }: SearchPageClientProps) {
	const searchParams = useSearchParams();
	const router = useRouter();
	const pathname = usePathname();
	const t = useTranslations("/search.page");

	const [searchResult, setSearchResult] = useState<NormalSearchResult>({
		products: [],
		totalCount: 0,
		categories: [],
		priceRange: { min: 0, max: 0 },
	});
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const query = searchParams.get("q") || initialSearchParams.q || "";
	const category = searchParams.get("category") || initialSearchParams.category;
	const sort = searchParams.get("sort") || initialSearchParams.sort;
	const minPrice = searchParams.get("min_price") || initialSearchParams.min_price;
	const maxPrice = searchParams.get("max_price") || initialSearchParams.max_price;

	const filters: NormalSearchFilters = {
		category: category || undefined,
		sortBy: (sort as NormalSearchFilters["sortBy"]) || "relevance",
		priceRange: minPrice || maxPrice ? {
			min: minPrice ? parseFloat(minPrice) : 0,
			max: maxPrice ? parseFloat(maxPrice) : Infinity,
		} : undefined,
	};

	const updateURL = useDebouncedCallback((newFilters: NormalSearchFilters) => {
		const params = new URLSearchParams(searchParams);
		
		if (newFilters.category) {
			params.set("category", newFilters.category);
		} else {
			params.delete("category");
		}

		if (newFilters.sortBy && newFilters.sortBy !== "relevance") {
			params.set("sort", newFilters.sortBy);
		} else {
			params.delete("sort");
		}

		if (newFilters.priceRange) {
			if (newFilters.priceRange.min > 0) {
				params.set("min_price", newFilters.priceRange.min.toString());
			} else {
				params.delete("min_price");
			}
			if (newFilters.priceRange.max < Infinity) {
				params.set("max_price", newFilters.priceRange.max.toString());
			} else {
				params.delete("max_price");
			}
		} else {
			params.delete("min_price");
			params.delete("max_price");
		}

		router.replace(`${pathname}?${params.toString()}`);
	}, 300);

	const handleFiltersChange = (newFilters: NormalSearchFilters) => {
		updateURL(newFilters);
	};

	const handleClearFilters = () => {
		const params = new URLSearchParams(searchParams);
		params.delete("category");
		params.delete("sort");
		params.delete("min_price");
		params.delete("max_price");
		router.replace(`${pathname}?${params.toString()}`);
	};

	const performSearch = async () => {
		if (!query) return;

		setIsLoading(true);
		setError(null);

		try {
			const result = await enhancedNormalSearch({
				query,
				filters,
				limit: 20,
				offset: 0,
			});
			setSearchResult(result);
		} catch (err) {
			console.error("Search failed:", err);
			setError("Search failed. Please try again.");
			setSearchResult({
				products: [],
				totalCount: 0,
				categories: [],
				priceRange: { min: 0, max: 0 },
			});
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		performSearch();
	}, [query, category, sort, minPrice, maxPrice]);

	if (isLoading) {
		return (
			<div className="space-y-6">
				<h1 className="text-3xl font-bold leading-none tracking-tight text-foreground">
					{t("title", { query })}
				</h1>
				<div>Loading...</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-6">
				<h1 className="text-3xl font-bold leading-none tracking-tight text-foreground">
					{t("title", { query })}
				</h1>
				<div className="text-red-500">{error}</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<h1 className="text-3xl font-bold leading-none tracking-tight text-foreground">
				{t("title", { query })}
			</h1>

			<SearchFilters
				filters={filters}
				categories={searchResult.categories}
				priceRange={searchResult.priceRange}
				onFiltersChange={handleFiltersChange}
				onClearFilters={handleClearFilters}
			/>

			<div className="space-y-4">
				<div className="text-sm text-muted-foreground">
					{searchResult.totalCount === 0
						? "No products found"
						: searchResult.totalCount === 1
						? "1 product found"
						: `${searchResult.totalCount} products found`}
				</div>

				{searchResult.products.length > 0 ? (
					<EnhancedProductList products={searchResult.products} showAddToCart={true} />
				) : (
					<ProductNotFound query={query} />
				)}
			</div>
		</div>
	);
}