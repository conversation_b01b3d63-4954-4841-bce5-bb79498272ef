"use client";

import { useState } from "react";
import type * as Commerce from "commerce-kit";
import Image from "next/image";
import { useLocale } from "next-intl";
import { formatMoney } from "@/lib/utils";
import { JsonLd, mappedProductsToJsonLd } from "@/ui/json-ld";
import { YnsLink } from "@/ui/yns-link";
import { Button } from "@/ui/shadcn/button";
import { addToCartAction } from "@/actions/cart-actions";

interface EnhancedProductListProps {
	products: Commerce.MappedProduct[];
	showAddToCart?: boolean;
}

export function EnhancedProductList({ products, showAddToCart = false }: EnhancedProductListProps) {
	const locale = useLocale();
	const [loadingProducts, setLoadingProducts] = useState<Set<string>>(new Set());
	const [successProducts, setSuccessProducts] = useState<Set<string>>(new Set());

	const validProducts = products.filter(
		(product) => product.metadata?.slug && typeof product.metadata.slug === "string",
	);

	const handleAddToCart = async (productId: string, event: React.MouseEvent) => {
		event.preventDefault();
		event.stopPropagation();

		setLoadingProducts(prev => new Set(prev).add(productId));
		setSuccessProducts(prev => {
			const newSet = new Set(prev);
			newSet.delete(productId);
			return newSet;
		});

		try {
			const formData = new FormData();
			formData.append("productId", productId);
			await addToCartAction(formData);
			
			setSuccessProducts(prev => new Set(prev).add(productId));
			setTimeout(() => {
				setSuccessProducts(prev => {
					const newSet = new Set(prev);
					newSet.delete(productId);
					return newSet;
				});
			}, 2000);
		} catch (error) {
			console.error("Failed to add to cart:", error);
		} finally {
			setLoadingProducts(prev => {
				const newSet = new Set(prev);
				newSet.delete(productId);
				return newSet;
			});
		}
	};

	return (
		<>
			<ul className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
				{validProducts.map((product, idx) => {
					const isLoading = loadingProducts.has(product.id);
					const isSuccess = successProducts.has(product.id);

					return (
						<li key={product.id} className="group">
							<YnsLink href={`/product/${product.metadata.slug}`}>
								<article className="overflow-hidden bg-white">
									{product.images[0] && (
										<div className="rounded-lg aspect-square w-full overflow-hidden bg-neutral-100 relative">
											<Image
												className="group-hover:rotate hover-perspective w-full bg-neutral-100 object-cover object-center transition-opacity group-hover:opacity-75"
												src={product.images[0]}
												width={768}
												height={768}
												loading={idx < 3 ? "eager" : "lazy"}
												priority={idx < 3}
												sizes="(max-width: 1024x) 100vw, (max-width: 1280px) 50vw, 700px"
												alt=""
											/>
										</div>
									)}
									<div className="p-2">
										<h2 className="text-xl font-medium text-neutral-700">{product.name}</h2>
										<footer className="flex items-center justify-between">
											<div className="text-base font-normal text-neutral-900">
												{product.default_price.unit_amount && (
													<p>
														{formatMoney({
															amount: product.default_price.unit_amount,
															currency: product.default_price.currency,
															locale,
														})}
													</p>
												)}
											</div>
											{showAddToCart && (
												<Button
													size="sm"
													variant={isSuccess ? "default" : "outline"}
													disabled={isLoading}
													onClick={(e) => handleAddToCart(product.id, e)}
													className="ml-2"
												>
													{isLoading ? "Adding..." : isSuccess ? "Added!" : "Add to Cart"}
												</Button>
											)}
										</footer>
									</div>
								</article>
							</YnsLink>
						</li>
					);
				})}
			</ul>
			<JsonLd jsonLd={mappedProductsToJsonLd(validProducts)} />
		</>
	);
}