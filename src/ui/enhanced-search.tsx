"use client";

import { ChevronDown, Filter, Search, X } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDebounce } from "use-debounce";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { SearchFilters, SearchResult } from "@/lib/search/enhanced-search";
import { ProductList } from "./enhanced-search/product-list";
import { YnsLink } from "./yns-link";

interface EnhancedSearchProps {
	initialQuery?: string;
	showAsModal?: boolean;
}

export function EnhancedSearch({ initialQuery = "", showAsModal = true }: EnhancedSearchProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [query, setQuery] = useState(initialQuery);
	const [debouncedQuery] = useDebounce(query, 300);
	const [searchResult, setSearchResult] = useState<SearchResult | null>(null);
	const [loading, setLoading] = useState(false);
	const [showFilters, setShowFilters] = useState(false);
	const [filters, setFilters] = useState<SearchFilters>({});
	const [mounted, setMounted] = useState(false);
	const inputRef = useRef<HTMLInputElement>(null);

	const pathname = usePathname();
	const router = useRouter();
	const searchParams = useSearchParams();

	// Handle client-side mounting
	useEffect(() => {
		setMounted(true);
	}, []);

	// Close search when navigating
	useEffect(() => {
		setIsOpen(false);
	}, [pathname]);

	// Load initial search from URL params (only on client side)
	useEffect(() => {
		if (!mounted) return;
		
		const urlQuery = searchParams.get("q");
		const urlCategory = searchParams.get("category");
		const urlSort = searchParams.get("sort");

		if (urlQuery) {
			setQuery(urlQuery);
		}
		if (urlCategory || urlSort) {
			setFilters((prev) => ({
				...prev,
				...(urlCategory && { category: urlCategory }),
				...(urlSort && { sortBy: urlSort as SearchFilters["sortBy"] }),
			}));
		}
	}, [searchParams, mounted]);

	const updateURL = useCallback(
		(newQuery: string, newFilters: SearchFilters) => {
			if (!mounted || typeof window === "undefined") return;
			
			const params = new URLSearchParams();
			if (newQuery.trim()) params.set("q", newQuery);
			if (newFilters.category) params.set("category", newFilters.category);
			if (newFilters.sortBy && newFilters.sortBy !== "relevance") params.set("sort", newFilters.sortBy);

			const newURL = params.toString() ? `${pathname}?${params.toString()}` : pathname;

			window.history.replaceState({}, "", newURL);
		},
		[pathname, mounted],
	);

	// Perform search when query or filters change
	useEffect(() => {
		if (!mounted) return;
		
		if (debouncedQuery.trim() || Object.keys(filters).length > 0) {
			performSearch();
			// Update URL after debounced query to avoid focus loss during typing
			updateURL(debouncedQuery, filters);
		} else {
			setSearchResult(null);
			updateURL("", filters);
		}
	}, [debouncedQuery, filters, updateURL, mounted]);

	const performSearch = useCallback(async () => {
		setLoading(true);
		try {
			const response = await fetch("/api/search", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					query: debouncedQuery,
					filters,
					limit: 20,
				}),
			});

			if (response.ok) {
				const result = (await response.json()) as SearchResult;
				setSearchResult(result);
			}
		} catch (error) {
			console.error("Search failed:", error);
		} finally {
			setLoading(false);
		}
	}, [debouncedQuery, filters]);

	const handleQueryChange = (value: string) => {
		setQuery(value);
		// Don't update URL immediately to prevent focus loss during typing
		// URL will be updated when search is performed via the debounced effect
	};

	const handleFilterChange = (newFilters: Partial<SearchFilters>) => {
		const updatedFilters = { ...filters, ...newFilters };
		setFilters(updatedFilters);
		updateURL(query, updatedFilters);
	};

	const clearFilters = () => {
		setFilters({});
		updateURL(query, {});
	};

	const handleSuggestionClick = (suggestion: string) => {
		setQuery(suggestion);
		// Update URL immediately for suggestion clicks since they're intentional
		updateURL(suggestion, filters);
	};

	const SearchContent = useMemo(() => {
		return (
			<Card className="w-full h-full rounded-none max-w-7xl mx-auto border-transparent">
				<CardContent className="p-6 h-full flex flex-col">
					{/* Search Input */}
					<div className="flex gap-4 mb-6">
						<div className="flex-1 relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
							<Input
								ref={inputRef}
								value={query}
								onChange={(e) => handleQueryChange(e.target.value)}
								placeholder="Search for products, categories, or brands..."
								className="pl-10 h-12 text-lg"
								key="search-input"
							/>
						</div>
						<Button variant="outline" onClick={() => setShowFilters(!showFilters)} className="h-12 px-4">
							<Filter className="h-4 w-4 mr-2" />
							Filters
						</Button>
					</div>

					{/* Filters */}
					{showFilters && (
						<div className="mb-6 p-4 bg-gray-50 rounded-lg">
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div>
									<Label htmlFor="category">Category</Label>
									<Select
										value={filters.category || "all"}
										onValueChange={(value) => handleFilterChange({ category: value === "all" ? undefined : value })}
									>
										<SelectTrigger>
											<SelectValue placeholder="All categories" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="all">All categories</SelectItem>
											{searchResult?.categories.map((category) => (
												<SelectItem key={category} value={category}>
													{category}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
								<div>
									<Label htmlFor="sort">Sort by</Label>
									<Select
										value={filters.sortBy || "relevance"}
										onValueChange={(value) =>
											handleFilterChange({ sortBy: value as SearchFilters["sortBy"] })
										}
									>
										<SelectTrigger>
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="relevance">Relevance</SelectItem>
											<SelectItem value="price-asc">Price: Low to High</SelectItem>
											<SelectItem value="price-desc">Price: High to Low</SelectItem>
											<SelectItem value="name">Name</SelectItem>
											<SelectItem value="newest">Newest</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-end">
									<Button variant="outline" onClick={clearFilters} className="w-full">
										<X className="h-4 w-4 mr-2" />
										Clear Filters
									</Button>
								</div>
							</div>
						</div>
					)}

					{/* Suggestions */}
					{searchResult?.suggestions && searchResult.suggestions.length > 0 && (
						<div className="mb-4">
							<p className="text-sm text-gray-600 mb-2">Suggestions:</p>
							<div className="flex flex-wrap gap-2">
								{searchResult.suggestions.map((suggestion) => (
									<Button
										key={suggestion}
										variant="outline"
										size="sm"
										onClick={() => handleSuggestionClick(suggestion)}
										className="text-xs"
									>
										{suggestion}
									</Button>
								))}
							</div>
						</div>
					)}

					{/* Results */}
					<div className="flex-1 overflow-auto">
						{!mounted && (
							<div className="flex items-center justify-center h-32">
								<div className="text-gray-500">Loading...</div>
							</div>
						)}

						{mounted && loading && (
							<div className="flex items-center justify-center h-32">
								<div className="text-gray-500">Searching...</div>
							</div>
						)}

						{mounted && !loading && searchResult && (
							<>
								<div className="mb-4">
									<p className="text-sm text-gray-600">{searchResult.totalCount} products found</p>
								</div>
								<ProductList products={searchResult.products} />
							</>
						)}

						{mounted && !loading && !searchResult && query.trim() === "" && (
							<div className="flex flex-col items-center justify-center h-full text-center">
								<Search className="h-16 w-16 text-gray-300 mb-4" />
								<h3 className="text-xl font-semibold text-gray-700 mb-2">Start searching for products</h3>
								<p className="text-gray-500 mb-6">
									Use the search bar above to find products, or try these popular searches:
								</p>
								<div className="flex flex-wrap gap-2">
									{["bags", "sunglasses", "apparel", "accessories"].map((term) => (
										<Button
											key={term}
											variant="outline"
											onClick={() => handleQueryChange(term)}
											className="capitalize"
										>
											{term}
										</Button>
									))}
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		);
	}, [
		query,
		loading,
		searchResult,
		showFilters,
		filters,
		mounted,
		handleQueryChange,
		handleFilterChange,
		clearFilters,
		handleSuggestionClick,
		inputRef,
	]);

	if (!showAsModal) {
		return SearchContent;
	}

	return (
		<div className="flex flex-col">
			{/* Search Bar */}
			<div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 px-4 py-3 border-b">
				<div className="flex items-center justify-between gap-x-4">
					<div className="mx-auto flex max-w-7xl items-center justify-between gap-x-4 w-full">
						<div className="flex items-center gap-x-4 flex-1">
							<p className="text-center text-sm font-medium text-gray-700">
								🔍 Find exactly what you're looking for
							</p>
							<Button
								size="sm"
								className="flex-none rounded-full bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-700 focus-visible:ring-0"
								onClick={() => setIsOpen(!isOpen)}
							>
								Enhanced Search <ChevronDown className="ml-1 h-4 w-4" />
							</Button>
						</div>
						<YnsLink
							className="bg-black rounded-full text-white px-4 py-1 text-sm hover:bg-gray-800 transition-colors"
							href="https://github.com/yournextstore/yournextstore"
							target="_blank"
						>
							View on GitHub
						</YnsLink>
					</div>
				</div>
			</div>

			{/* Search Modal */}
			<div
				className={`fixed top-0 left-0 right-0 bg-white transition-all duration-300 ease-in-out shadow-lg z-50 ${
					isOpen ? "h-2/3" : "h-0 overflow-hidden"
				}`}
			>
				{SearchContent}
			</div>

			{/* Backdrop */}
			{isOpen && (
				<div
					className="fixed inset-0 bg-black bg-opacity-50 transition-opacity ease-in-out duration-300 z-40"
					onClick={() => setIsOpen(false)}
				/>
			)}
		</div>
	);
}
