import * as Commerce from "commerce-kit";
import { safeProductBrowse } from "@/lib/safe-commerce";
import Fuse, { type IFuseOptions } from "fuse.js";
import { unstable_cache } from "next/cache";

export interface NormalSearchFilters {
	category?: string;
	priceRange?: {
		min: number;
		max: number;
	};
	sortBy?: "relevance" | "price-asc" | "price-desc" | "name" | "newest";
}

export interface NormalSearchResult {
	products: Commerce.MappedProduct[];
	totalCount: number;
	categories: string[];
	priceRange: {
		min: number;
		max: number;
	};
}

export interface NormalSearchOptions {
	query: string;
	filters?: NormalSearchFilters;
	limit?: number;
	offset?: number;
}

// Fuse.js configuration for fuzzy search
const fuseOptions: IFuseOptions<Commerce.MappedProduct> = {
	keys: [
		{ name: "name", weight: 0.7 },
		{ name: "description", weight: 0.3 },
		{ name: "metadata.category", weight: 0.5 },
		{ name: "metadata.variant", weight: 0.2 },
	],
	threshold: 0.4, // Lower = more strict matching
	distance: 100,
	minMatchCharLength: 2,
	includeScore: true,
	includeMatches: true,
};

// Cache for search index
let searchIndex: Fuse<Commerce.MappedProduct> | null = null;
let cachedProducts: Commerce.MappedProduct[] = [];

async function getSearchIndex(): Promise<Fuse<Commerce.MappedProduct>> {
	if (searchIndex && cachedProducts.length > 0) {
		return searchIndex;
	}

	try {
		const products = await safeProductBrowse({ first: 1000 });
		cachedProducts = products;
		searchIndex = new Fuse(products, fuseOptions);
		return searchIndex;
	} catch (error) {
		console.error("Failed to build search index:", error);
		throw new Error("Search index unavailable");
	}
}

function applyFilters(products: Commerce.MappedProduct[], filters?: NormalSearchFilters): Commerce.MappedProduct[] {
	if (!filters) return products;

	let filtered = products;

	// Category filter
	if (filters.category) {
		filtered = filtered.filter(
			(product) => product.metadata?.category?.toLowerCase() === filters.category?.toLowerCase(),
		);
	}

	// Price range filter
	if (filters.priceRange) {
		filtered = filtered.filter((product) => {
			const price = product.default_price.unit_amount || 0;
			return (
				price >= (filters.priceRange?.min || 0) * 100 && // Convert to cents
				price <= (filters.priceRange?.max || Infinity) * 100
			);
		});
	}

	return filtered;
}

function sortProducts(
	products: Commerce.MappedProduct[],
	sortBy?: NormalSearchFilters["sortBy"],
): Commerce.MappedProduct[] {
	if (!sortBy || sortBy === "relevance") return products;

	return [...products].sort((a, b) => {
		switch (sortBy) {
			case "price-asc":
				return (a.default_price.unit_amount || 0) - (b.default_price.unit_amount || 0);
			case "price-desc":
				return (b.default_price.unit_amount || 0) - (a.default_price.unit_amount || 0);
			case "name":
				return a.name.localeCompare(b.name);
			case "newest":
				return new Date(b.created).getTime() - new Date(a.created).getTime();
			default:
				return 0;
		}
	});
}

function extractMetadata(products: Commerce.MappedProduct[]) {
	const categories = new Set<string>();
	let minPrice = Infinity;
	let maxPrice = 0;

	products.forEach((product) => {
		if (product.metadata?.category) {
			categories.add(product.metadata.category);
		}
		const price = (product.default_price.unit_amount || 0) / 100; // Convert from cents
		minPrice = Math.min(minPrice, price);
		maxPrice = Math.max(maxPrice, price);
	});

	return {
		categories: Array.from(categories),
		priceRange: {
			min: minPrice === Infinity ? 0 : minPrice,
			max: maxPrice,
		},
	};
}

export const enhancedNormalSearch = unstable_cache(
	async (options: NormalSearchOptions): Promise<NormalSearchResult> => {
		const { query, filters, limit = 20, offset = 0 } = options;

		try {
			const fuse = await getSearchIndex();
			let results: Commerce.MappedProduct[];

			if (query.trim()) {
				// Perform fuzzy search
				const fuseResults = fuse.search(query);
				results = fuseResults.map((result) => result.item);
			} else {
				// No query, return all products
				results = cachedProducts;
			}

			// Apply filters
			const filteredResults = applyFilters(results, filters);

			// Sort results
			const sortedResults = sortProducts(filteredResults, filters?.sortBy);

			// Paginate
			const paginatedResults = sortedResults.slice(offset, offset + limit);

			// Generate metadata
			const metadata = extractMetadata(cachedProducts);

			return {
				products: paginatedResults,
				totalCount: sortedResults.length,
				categories: metadata.categories,
				priceRange: metadata.priceRange,
			};
		} catch (error) {
			console.error("Enhanced normal search failed:", error);
			return {
				products: [],
				totalCount: 0,
				categories: [],
				priceRange: { min: 0, max: 0 },
			};
		}
	},
	["enhanced-normal-search"],
	{
		tags: ["enhanced-normal-search", "products"],
		revalidate: 300, // Cache for 5 minutes
	},
);