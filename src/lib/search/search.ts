import * as Commerce from "commerce-kit";
import { unstable_cache } from "next/cache";
import { enhancedNormalSearch } from "./enhanced-normal-search";

export const searchProducts = unstable_cache(
	async (query: string) => {
		try {
			const result = await enhancedNormalSearch({
				query,
				filters: { sortBy: "relevance" },
				limit: 100,
				offset: 0,
			});
			return result.products;
		} catch (error) {
			console.error("Failed to search products:", error);
			return [];
		}
	},
	["search", "products"],
	{
		tags: ["search", "products"],
	},
);
